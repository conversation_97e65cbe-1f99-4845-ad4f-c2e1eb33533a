"""
Nacos配置中心管理器
提供单例模式的Nacos配置管理，支持配置热更新和降级处理
"""
import json
import threading
import time
import yaml
from typing import Dict, Any, Optional, Callable
from loguru import logger
import nacos


class NacosConfigManager:
    """
    Nacos配置中心管理器 - 单例模式
    
    功能特性：
    1. 单例模式确保全局唯一实例
    2. 支持从不同的groupId和dataId获取动态配置
    3. 支持配置热更新（监听配置变化）
    4. 提供线程安全的配置获取方法
    5. 当Nacos不可用时，能够降级到本地配置
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(NacosConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化Nacos配置管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._client = None
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._listeners: Dict[str, list] = {}
        self._config_lock = threading.RLock()
        self._is_connected = False
        self._fallback_configs: Dict[str, Dict[str, Any]] = {}
        
        # 从环境配置初始化
        self._init_from_env_config()
    
    def _init_from_env_config(self):
        """从环境配置初始化Nacos客户端"""
        try:
            from .environments import env_manager
            config = env_manager.get_config()
            
            self.endpoint = config.nacos_endpoint

            if self.endpoint and self.default_data_id:
                self._connect()
            else:
                logger.warning("[NacosConfig] Nacos配置不完整，将使用降级模式")
                
        except Exception as e:
            logger.error(f"[NacosConfig] 初始化环境配置失败: {e}")
    
    def _connect(self):
        """连接到Nacos服务器"""
        try:
            # 解析endpoint
            if "://" in self.endpoint:
                # 如果是完整URL，提取host和port
                from urllib.parse import urlparse
                parsed = urlparse(self.endpoint)
                server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
            else:
                server_addresses = self.endpoint
            
            self._client = nacos.NacosClient(
                server_addresses=server_addresses,
                namespace="",  # 使用默认命名空间
                username="",   # 如果需要认证，可以配置
                password=""
            )
            
            # 测试连接
            test_config = self._client.get_config(
                data_id=self.default_data_id,
                group=self.default_group,
                timeout=3
            )
            
            self._is_connected = True
            logger.info(f"[NacosConfig] 连接Nacos成功: {server_addresses}")
            
            # 加载默认配置
            if test_config:
                self._load_config(self.default_data_id, self.default_group)
                
        except Exception as e:
            logger.error(f"[NacosConfig] 连接Nacos失败: {e}")
            self._is_connected = False
            self._client = None
    
    def _load_config(self, data_id: str, group: str = None) -> Optional[Dict[str, Any]]:
        """加载配置"""
        if group is None:
            group = self.default_group
            
        config_key = f"{group}:{data_id}"
        
        try:
            if not self._client:
                logger.warning(f"[NacosConfig] Nacos客户端未连接，使用降级配置: {config_key}")
                return self._get_fallback_config(config_key)
            
            # 从Nacos获取配置
            config_content = self._client.get_config(
                data_id=data_id,
                group=group,
                timeout=5
            )
            
            if not config_content:
                logger.warning(f"[NacosConfig] 配置为空: {config_key}")
                return self._get_fallback_config(config_key)
            
            # 解析配置内容
            parsed_config = self._parse_config_content(config_content)
            
            # 缓存配置
            with self._config_lock:
                self._config_cache[config_key] = parsed_config
                # 同时保存为降级配置
                self._fallback_configs[config_key] = parsed_config.copy()
            
            logger.info(f"[NacosConfig] 配置加载成功: {config_key}")
            return parsed_config
            
        except Exception as e:
            logger.error(f"[NacosConfig] 加载配置失败: {config_key}, error: {e}")
            return self._get_fallback_config(config_key)
    
    def _parse_config_content(self, content: str) -> Dict[str, Any]:
        """解析配置内容，支持JSON和YAML格式"""
        content = content.strip()
        
        if not content:
            return {}
        
        # 尝试解析JSON
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            pass
        
        # 尝试解析YAML
        try:
            return yaml.safe_load(content) or {}
        except yaml.YAMLError:
            pass
        
        # 如果都失败，返回原始字符串
        logger.warning("[NacosConfig] 无法解析配置内容，返回原始字符串")
        return {"raw_content": content}
    
    def _get_fallback_config(self, config_key: str) -> Dict[str, Any]:
        """获取降级配置"""
        with self._config_lock:
            fallback = self._fallback_configs.get(config_key, {})
            if fallback:
                logger.info(f"[NacosConfig] 使用降级配置: {config_key}")
            return fallback
    
    def get_config(self, data_id: str = None, group: str = None) -> Dict[str, Any]:
        """
        获取配置
        
        Args:
            data_id: 配置ID，默认使用环境配置中的data_id
            group: 配置组，默认使用环境配置中的group
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        if data_id is None:
            data_id = self.default_data_id
        if group is None:
            group = self.default_group
            
        config_key = f"{group}:{data_id}"
        
        # 先从缓存获取
        with self._config_lock:
            cached_config = self._config_cache.get(config_key)
            if cached_config is not None:
                return cached_config.copy()
        
        # 缓存中没有，重新加载
        return self._load_config(data_id, group) or {}
    
    def get_config_value(self, key: str, default: Any = None, data_id: str = None, group: str = None) -> Any:
        """
        获取配置中的特定值
        
        Args:
            key: 配置键，支持点分隔的嵌套键如 'database.host'
            default: 默认值
            data_id: 配置ID
            group: 配置组
            
        Returns:
            Any: 配置值
        """
        config = self.get_config(data_id, group)
        
        # 支持嵌套键访问
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                if isinstance(value, dict):
                    value = value.get(k)
                else:
                    return default
            return value if value is not None else default
        except (KeyError, TypeError):
            return default

    def add_config_listener(self, callback: Callable[[Dict[str, Any]], None],
                          data_id: str = None, group: str = None):
        """
        添加配置变更监听器

        Args:
            callback: 配置变更回调函数，接收新配置作为参数
            data_id: 配置ID
            group: 配置组
        """
        if data_id is None:
            data_id = self.default_data_id
        if group is None:
            group = self.default_group

        config_key = f"{group}:{data_id}"

        with self._config_lock:
            if config_key not in self._listeners:
                self._listeners[config_key] = []
            self._listeners[config_key].append(callback)

        # 如果Nacos客户端可用，添加服务端监听
        if self._client and self._is_connected:
            try:
                def nacos_callback(args):
                    """Nacos配置变更回调"""
                    try:
                        new_content = args.get('content', '')
                        new_config = self._parse_config_content(new_content)

                        # 更新缓存
                        with self._config_lock:
                            self._config_cache[config_key] = new_config
                            self._fallback_configs[config_key] = new_config.copy()

                        logger.info(f"[NacosConfig] 配置已更新: {config_key}")

                        # 通知所有监听器
                        self._notify_listeners(config_key, new_config)

                    except Exception as e:
                        logger.error(f"[NacosConfig] 处理配置变更失败: {e}")

                self._client.add_config_watcher(
                    data_id=data_id,
                    group=group,
                    cb=nacos_callback
                )

                logger.info(f"[NacosConfig] 配置监听器已添加: {config_key}")

            except Exception as e:
                logger.error(f"[NacosConfig] 添加Nacos监听器失败: {e}")

    def _notify_listeners(self, config_key: str, new_config: Dict[str, Any]):
        """通知配置变更监听器"""
        with self._config_lock:
            listeners = self._listeners.get(config_key, [])

        for callback in listeners:
            try:
                callback(new_config.copy())
            except Exception as e:
                logger.error(f"[NacosConfig] 配置监听器回调失败: {e}")

    def refresh_config(self, data_id: str = None, group: str = None) -> bool:
        """
        手动刷新配置

        Args:
            data_id: 配置ID
            group: 配置组

        Returns:
            bool: 是否刷新成功
        """
        if data_id is None:
            data_id = self.default_data_id
        if group is None:
            group = self.default_group

        config_key = f"{group}:{data_id}"

        try:
            # 清除缓存
            with self._config_lock:
                self._config_cache.pop(config_key, None)

            # 重新加载
            new_config = self._load_config(data_id, group)

            if new_config is not None:
                # 通知监听器
                self._notify_listeners(config_key, new_config)
                logger.info(f"[NacosConfig] 配置刷新成功: {config_key}")
                return True
            else:
                logger.warning(f"[NacosConfig] 配置刷新失败: {config_key}")
                return False

        except Exception as e:
            logger.error(f"[NacosConfig] 配置刷新异常: {config_key}, error: {e}")
            return False

    def is_connected(self) -> bool:
        """检查Nacos连接状态"""
        return self._is_connected and self._client is not None

    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "endpoint": getattr(self, 'endpoint', ''),
            "default_data_id": getattr(self, 'default_data_id', ''),
            "default_group": getattr(self, 'default_group', ''),
            "is_connected": self._is_connected,
            "cached_configs": list(self._config_cache.keys()),
            "fallback_configs": list(self._fallback_configs.keys())
        }

    def set_fallback_config(self, config: Dict[str, Any], data_id: str = None, group: str = None):
        """
        设置降级配置

        Args:
            config: 降级配置字典
            data_id: 配置ID
            group: 配置组
        """
        if data_id is None:
            data_id = self.default_data_id
        if group is None:
            group = self.default_group

        config_key = f"{group}:{data_id}"

        with self._config_lock:
            self._fallback_configs[config_key] = config.copy()

        logger.info(f"[NacosConfig] 降级配置已设置: {config_key}")


# 全局单例实例
nacos_config_manager = NacosConfigManager()
