#!/usr/bin/env python3
"""
Alpha Service 启动脚本
"""

# 在导入任何其他模块之前，先设置早期日志拦截
import src.shared.logging.early_intercept

import asyncio
import uvicorn

# 初始化日志配置
from src.shared.logging.logger import logger

async def start_service():
    """启动服务"""
    try:
        logger.info("🚀 Alpha Service 启动中...")

        # 初始化无AK认证环境（在导入应用之前）
        try:
            from src.shared.auth import init_akless_auth
            logger.info("初始化无AK认证环境...")
            akless_success = init_akless_auth()
            if akless_success:
                logger.info("✅ 无AK认证环境初始化成功")
            else:
                logger.warning("⚠️ 无AK认证环境初始化失败，RAG客户端可能无法正常工作")
        except Exception as e:
            logger.error(f"无AK认证环境初始化异常: {e}")
            # 不抛出异常，允许应用继续启动

        # 导入应用
        from src.presentation.api.pythonic_server import pythonic_app as app

        # 计算最佳worker数量
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        worker_count = min(cpu_count * 2 + 1, 16)  # 最多16个worker

        logger.info(f"CPU核心数: {cpu_count}, 启动Worker数: {worker_count}")

        # 尝试使用gunicorn启动（支持多worker）
        try:
            import gunicorn.app.base

            class StandaloneApplication(gunicorn.app.base.BaseApplication):
                def __init__(self, app, options=None):
                    self.options = options or {}
                    self.application = app
                    super().__init__()

                def load_config(self):
                    for key, value in self.options.items():
                        self.cfg.set(key.lower(), value)

                def load(self):
                    return self.application

            options = {
                'bind': '0.0.0.0:8000',
                'workers': worker_count,
                'worker_class': 'uvicorn.workers.UvicornWorker',
                'worker_connections': 1000,
                'max_requests': 1000,
                'max_requests_jitter': 100,
                'timeout': 30,
                'keepalive': 2,
                'preload_app': True,
                'access_log': False,
                'error_log': '-',
                'log_level': 'info'
            }

            logger.info("使用Gunicorn + UvicornWorker启动服务")
            StandaloneApplication(app, options).run()

        except ImportError:
            # 如果没有gunicorn，回退到uvicorn单进程
            logger.warning("未安装gunicorn，使用uvicorn单进程模式")
            config = uvicorn.Config(
                app=app,
                host="0.0.0.0",
                port=8000,
                log_level="info",
                reload=False,
                access_log=False
            )
            server = uvicorn.Server(config)
            await server.serve()

    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(start_service()) 