#!/usr/bin/env python3
"""
调试Diamond配置请求，分析实际的HTTP请求和响应
"""

import sys
import os
import requests
from pathlib import Path
from urllib.parse import urlencode

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def debug_diamond_request():
    """调试Diamond配置请求"""
    print("🔍 调试Diamond配置请求")
    
    try:
        from shared.config.environments import env_manager
        env_config = env_manager.get_config()
        
        # 获取配置的endpoint
        original_endpoint = env_config.diamond_endpoint
        backup_endpoint = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
        
        print(f"📋 配置信息:")
        print(f"  - 原始endpoint: {original_endpoint}")
        print(f"  - 备用endpoint: {backup_endpoint}")
        print(f"  - 默认data_id: {env_config.diamond_data_id}")
        print(f"  - 默认group: DEFAULT_GROUP")
        
        # 测试不同的请求
        test_requests = [
            {
                "name": "测试1: 获取默认配置",
                "endpoint": backup_endpoint,
                "params": {
                    'dataId': env_config.diamond_data_id,
                    'group': 'DEFAULT_GROUP'
                }
            },
            {
                "name": "测试2: 获取AIPPT配置",
                "endpoint": backup_endpoint,
                "params": {
                    'dataId': 'wuying-alpha-service:aippt_config',
                    'group': 'DEFAULT_GROUP'
                }
            },
            {
                "name": "测试3: 直接访问endpoint（无参数）",
                "endpoint": backup_endpoint,
                "params": {}
            },
            {
                "name": "测试4: 测试不存在的配置",
                "endpoint": backup_endpoint,
                "params": {
                    'dataId': 'non-existent-config',
                    'group': 'DEFAULT_GROUP'
                }
            }
        ]
        
        for test in test_requests:
            print(f"\n{'='*60}")
            print(f"🧪 {test['name']}")
            print(f"{'='*60}")
            
            try:
                # 构建URL
                if test['params']:
                    url = f"{test['endpoint']}?{urlencode(test['params'])}"
                else:
                    url = test['endpoint']
                
                print(f"🔗 请求URL: {url}")
                
                # 发送请求
                headers = {
                    'User-Agent': 'Diamond-Client/1.0',
                    'Accept': 'text/plain,application/json,*/*'
                }
                
                response = requests.get(url, headers=headers, timeout=10)
                
                print(f"📊 响应状态: HTTP {response.status_code}")
                print(f"📋 响应头:")
                for key, value in response.headers.items():
                    print(f"     {key}: {value}")
                
                content = response.text
                print(f"📄 响应内容长度: {len(content)} 字符")
                
                if content:
                    # 分析内容类型
                    lines = content.strip().split('\n')
                    print(f"📝 内容分析:")
                    print(f"     行数: {len(lines)}")
                    print(f"     前3行: {lines[:3]}")
                    
                    # 检查是否为IP地址
                    if lines and all(is_ip_like(line.strip()) for line in lines[:5] if line.strip()):
                        print(f"     ⚠️  内容疑似为IP地址列表")
                    
                    # 检查是否为JSON
                    try:
                        import json
                        json.loads(content)
                        print(f"     ✅ 内容为有效JSON")
                    except:
                        print(f"     ❌ 内容不是JSON格式")
                    
                    # 显示内容预览
                    preview = content[:200] + "..." if len(content) > 200 else content
                    print(f"📖 内容预览:")
                    print(f"     {preview}")
                else:
                    print(f"❌ 响应内容为空")
                    
            except Exception as e:
                print(f"❌ 请求失败: {e}")
        
        # 额外测试：尝试不同的API路径
        print(f"\n{'='*60}")
        print(f"🧪 测试5: 尝试不同的API路径")
        print(f"{'='*60}")
        
        base_url = "http://jmenv.tbsite.net:8080"
        api_paths = [
            "/diamond-server/diamond",
            "/diamond-server/config.co",
            "/diamond-server/basestone.do",
            "/diamond-server/admin.do",
            "/nacos/v1/cs/configs"
        ]
        
        for api_path in api_paths:
            try:
                url = f"{base_url}{api_path}"
                print(f"\n🔗 测试路径: {url}")
                
                response = requests.get(url, timeout=5)
                print(f"   状态: HTTP {response.status_code}")
                
                if response.status_code == 200:
                    content_preview = response.text[:100] + "..." if len(response.text) > 100 else response.text
                    print(f"   内容预览: {content_preview}")
                    
            except Exception as e:
                print(f"   错误: {e}")
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def is_ip_like(text):
    """检查文本是否像IP地址"""
    parts = text.split('.')
    if len(parts) != 4:
        return False
    
    try:
        for part in parts:
            num = int(part)
            if not (0 <= num <= 255):
                return False
        return True
    except ValueError:
        return False


def test_dns_resolution():
    """测试DNS解析"""
    print(f"\n{'='*60}")
    print(f"🌐 DNS解析测试")
    print(f"{'='*60}")
    
    import socket
    
    hostnames = [
        "jmenv.tbsite.net",
        "pre-jmenv.cn-hangzhou.aliyun-inc.com"
    ]
    
    for hostname in hostnames:
        try:
            print(f"\n🔍 解析: {hostname}")
            ip_list = socket.gethostbyname_ex(hostname)
            print(f"   主机名: {ip_list[0]}")
            print(f"   别名: {ip_list[1]}")
            print(f"   IP地址: {ip_list[2]}")
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")


def main():
    """主函数"""
    print("🚀 Diamond配置请求调试工具")
    print(f"📅 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # DNS解析测试
    test_dns_resolution()
    
    # 请求调试
    debug_diamond_request()
    
    print(f"\n🏁 调试完成")


if __name__ == "__main__":
    import time
    main()
