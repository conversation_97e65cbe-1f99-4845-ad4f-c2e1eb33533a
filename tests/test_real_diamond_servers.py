#!/usr/bin/env python3
"""
使用真实的Diamond服务器IP地址获取配置
"""

import sys
import os
import requests
import time
from pathlib import Path
from urllib.parse import urlencode

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def get_server_ips():
    """获取真实的服务器IP列表"""
    # 这些是从负载均衡器返回的真实服务器IP
    server_ips = [
        "************",
        "************", 
        "************",
        "************",
        "************",
        "************",
        "***********",
        "***********0",
        "***********1",
        "***********2",
        "***********3",
        "***********4",
        "***********6",
        "***********8",
        "***********9",
        "************"
    ]
    return server_ips


def test_diamond_server(server_ip, port=8080):
    """测试单个Diamond服务器"""
    print(f"\n🔍 测试服务器: {server_ip}:{port}")
    
    # 测试不同的API路径
    api_paths = [
        "/diamond-server/diamond",
        "/diamond-server/config.co", 
        "/diamond-server/basestone.do",
        "/nacos/v1/cs/configs"
    ]
    
    for api_path in api_paths:
        try:
            base_url = f"http://{server_ip}:{port}{api_path}"
            
            # 测试获取AIPPT配置
            params = {
                'dataId': 'wuying-alpha-service:aippt_config',
                'group': 'DEFAULT_GROUP'
            }
            
            url = f"{base_url}?{urlencode(params)}"
            
            print(f"  🔗 尝试: {api_path}")
            print(f"     URL: {url}")
            
            headers = {
                'User-Agent': 'Diamond-Client/1.0',
                'Accept': 'text/plain,application/json,*/*'
            }
            
            response = requests.get(url, headers=headers, timeout=5)
            
            print(f"     状态: HTTP {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"     长度: {len(content)} 字符")
                
                # 检查内容是否为IP列表（说明这也是负载均衡器）
                lines = content.strip().split('\n')
                if len(lines) > 10 and all(is_ip_like(line.strip()) for line in lines[:5] if line.strip()):
                    print(f"     ⚠️  返回IP列表（负载均衡器）")
                else:
                    print(f"     ✅ 可能是真实配置内容")
                    print(f"     预览: {content[:100]}...")
                    return server_ip, api_path, content
                    
            elif response.status_code == 404:
                print(f"     ❌ 配置不存在")
            else:
                print(f"     ❌ 错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"     ⏰ 超时")
        except requests.exceptions.ConnectionError:
            print(f"     🔌 连接失败")
        except Exception as e:
            print(f"     ❌ 异常: {e}")
    
    return None


def is_ip_like(text):
    """检查是否为IP地址格式"""
    parts = text.split('.')
    if len(parts) != 4:
        return False
    
    try:
        for part in parts:
            num = int(part)
            if not (0 <= num <= 255):
                return False
        return True
    except ValueError:
        return False


def test_nacos_client_with_servers():
    """使用Nacos客户端测试真实服务器"""
    print(f"\n{'='*60}")
    print(f"🧪 使用Nacos客户端测试真实服务器")
    print(f"{'='*60}")
    
    try:
        import nacos
        
        server_ips = get_server_ips()
        
        # 尝试前几个服务器
        for server_ip in server_ips[:5]:
            try:
                print(f"\n🔍 测试Nacos客户端连接: {server_ip}:8080")
                
                client = nacos.NacosClient(
                    server_addresses=f"{server_ip}:8080",
                    namespace="",
                    username="",
                    password=""
                )
                
                # 尝试获取配置
                config = client.get_config(
                    data_id="wuying-alpha-service:aippt_config",
                    group="DEFAULT_GROUP",
                    timeout=5
                )
                
                if config:
                    print(f"✅ 成功获取配置 ({len(config)} 字符)")
                    
                    # 检查是否为IP列表
                    lines = config.strip().split('\n')
                    if len(lines) > 10 and all(is_ip_like(line.strip()) for line in lines[:5] if line.strip()):
                        print(f"⚠️  仍然是IP列表")
                    else:
                        print(f"✅ 可能是真实配置")
                        print(f"预览: {config[:200]}...")
                        return config
                else:
                    print(f"❌ 配置为空")
                    
            except Exception as e:
                print(f"❌ 连接失败: {e}")
        
    except ImportError:
        print("❌ nacos-sdk-python未安装")
    
    return None


def try_different_ports():
    """尝试不同的端口"""
    print(f"\n{'='*60}")
    print(f"🧪 尝试不同端口")
    print(f"{'='*60}")
    
    server_ip = "************"  # 使用第一个IP
    ports = [8080, 8848, 9848, 7001, 8000]
    
    for port in ports:
        try:
            print(f"\n🔍 测试端口: {server_ip}:{port}")
            
            url = f"http://{server_ip}:{port}/diamond-server/diamond"
            params = {
                'dataId': 'wuying-alpha-service:aippt_config',
                'group': 'DEFAULT_GROUP'
            }
            
            full_url = f"{url}?{urlencode(params)}"
            
            response = requests.get(full_url, timeout=5)
            print(f"  状态: HTTP {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"  长度: {len(content)} 字符")
                
                # 检查内容
                lines = content.strip().split('\n')
                if not (len(lines) > 10 and all(is_ip_like(line.strip()) for line in lines[:5] if line.strip())):
                    print(f"  ✅ 可能找到真实配置")
                    print(f"  预览: {content[:100]}...")
                    return content
                else:
                    print(f"  ⚠️  仍然是IP列表")
                    
        except Exception as e:
            print(f"  ❌ 错误: {e}")
    
    return None


def main():
    """主函数"""
    print("🚀 测试真实Diamond服务器")
    print(f"📅 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    server_ips = get_server_ips()
    print(f"📋 获取到 {len(server_ips)} 个服务器IP")
    
    # 测试前几个服务器
    print(f"\n{'='*60}")
    print(f"🧪 HTTP方式测试服务器")
    print(f"{'='*60}")
    
    found_config = None
    
    for i, server_ip in enumerate(server_ips[:8]):  # 只测试前8个
        print(f"\n[{i+1}/{len(server_ips[:8])}] 测试服务器: {server_ip}")
        result = test_diamond_server(server_ip)
        if result:
            server_ip, api_path, content = result
            print(f"🎉 找到真实配置！")
            print(f"   服务器: {server_ip}")
            print(f"   API路径: {api_path}")
            found_config = content
            break
    
    # 如果HTTP方式没找到，尝试Nacos客户端
    if not found_config:
        found_config = test_nacos_client_with_servers()
    
    # 如果还没找到，尝试不同端口
    if not found_config:
        found_config = try_different_ports()
    
    print(f"\n🏁 测试完成")
    
    if found_config:
        print("✅ 成功找到真实配置")
        
        # 保存配置
        try:
            output_file = Path(__file__).parent / "real_aippt_config.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(found_config)
            print(f"💾 配置已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            
        return True
    else:
        print("❌ 未找到真实配置")
        print("💡 可能的原因:")
        print("   1. 所有服务器都是负载均衡器")
        print("   2. 真实配置服务器使用不同的端口或协议")
        print("   3. 需要特殊的认证或参数")
        print("   4. 配置确实不存在")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
